from aiogram import Router, F
from aiogram.types import Message, CallbackQuery
from aiogram.filters import CommandStart
from aiogram.fsm.context import FSMContext
from aiogram.fsm.state import StatesGroup, State
from ..keyboards.main import get_curator_main_menu_kb

router = Router()

class CuratorMainStates(StatesGroup):
    main = State()

# Обработчик /start убран - используется общий в main.py

async def show_curator_main_menu(message: Message, user_role: str = None):
    """Показать главное меню куратора"""
    # Проверяем права доступа (админы тоже могут заходить в меню куратора)
    if user_role not in ["admin", "curator"]:
        return

    await message.answer(
        "Добро пожаловать в панель куратора!\n"
        "Выберите действие из меню ниже:",
        reply_markup=get_curator_main_menu_kb()
    )