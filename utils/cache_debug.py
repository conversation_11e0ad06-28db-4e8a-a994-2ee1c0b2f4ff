"""
Утилиты для отладки кеша ролей
"""
import asyncio
import logging
from middlewares.role_middleware import clear_role_cache, force_update_role_cache, _global_role_cache, _global_cache_updated, _last_cache_update
from utils.redis_manager import redis_manager


async def debug_role_cache():
    """Показать состояние кеша ролей"""
    print("🔍 === СОСТОЯНИЕ КЕША РОЛЕЙ ===")
    print(f"Cache updated: {_global_cache_updated}")
    print(f"Last update: {_last_cache_update}")
    print(f"Cache content: {_global_role_cache}")
    
    # Проверяем Redis
    if redis_manager.connected:
        try:
            redis_data = await redis_manager.get("user_roles_cache")
            print(f"Redis data: {redis_data}")
        except Exception as e:
            print(f"Redis error: {e}")
    else:
        print("Redis not connected")


async def force_clear_all_cache():
    """Принудительно очистить весь кеш"""
    print("🗑️ Принудительная очистка всего кеша...")
    
    # Очищаем глобальный кеш
    await clear_role_cache()
    
    # Дополнительно очищаем Redis напрямую
    if redis_manager.connected:
        await redis_manager.delete("user_roles_cache")
        print("🗑️ Redis кеш очищен напрямую")
    
    print("✅ Весь кеш очищен")


async def test_role_cache_for_user(telegram_id: int):
    """Тестировать кеш для конкретного пользователя"""
    print(f"🧪 === ТЕСТ КЕША ДЛЯ ПОЛЬЗОВАТЕЛЯ {telegram_id} ===")
    
    # Показываем текущее состояние
    await debug_role_cache()
    
    # Проверяем роль в кеше
    role_found = None
    for role_name, user_ids in _global_role_cache.items():
        if telegram_id in user_ids:
            role_found = role_name
            break
    
    print(f"Роль в кеше: {role_found}")
    
    # Проверяем в БД
    try:
        from database import UserRepository
        user = await UserRepository.get_by_telegram_id(telegram_id)
        db_role = user.role if user else "not_found"
        print(f"Роль в БД: {db_role}")
        
        if role_found != db_role:
            print("❌ НЕСООТВЕТСТВИЕ! Кеш не синхронизирован с БД")
            return False
        else:
            print("✅ Кеш синхронизирован с БД")
            return True
            
    except Exception as e:
        print(f"❌ Ошибка проверки БД: {e}")
        return False


if __name__ == "__main__":
    # Пример использования
    async def main():
        await debug_role_cache()
        await test_role_cache_for_user(7265679697)
        
    asyncio.run(main())
