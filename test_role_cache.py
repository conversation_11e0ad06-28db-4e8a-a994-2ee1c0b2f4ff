#!/usr/bin/env python3
"""
Скрипт для тестирования кеша ролей
"""
import asyncio
import sys
import os

# Добавляем корневую директорию в путь
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from utils.cache_debug import debug_role_cache, test_role_cache_for_user, force_clear_all_cache
from middlewares.role_middleware import force_update_role_cache, clear_role_cache
from database import UserRepository
from utils.redis_manager import redis_manager


async def test_user_role_changes(telegram_id: int):
    """Тестировать изменения роли пользователя"""
    print(f"\n🧪 === ТЕСТ ИЗМЕНЕНИЯ РОЛИ ДЛЯ ПОЛЬЗОВАТЕЛЯ {telegram_id} ===")
    
    # Подключаемся к Redis
    await redis_manager.connect()
    
    # 1. Показываем текущее состояние
    print("\n1. Текущее состояние:")
    await test_role_cache_for_user(telegram_id)
    
    # 2. Получаем пользователя из БД
    user = await UserRepository.get_by_telegram_id(telegram_id)
    if not user:
        print(f"❌ Пользователь {telegram_id} не найден в БД")
        return
    
    print(f"\n2. Пользователь в БД: {user.name}, роль: {user.role}")
    
    # 3. Очищаем кеш полностью
    print("\n3. Очищаем кеш полностью...")
    await force_clear_all_cache()
    
    # 4. Проверяем состояние после очистки
    print("\n4. Состояние после очистки:")
    await debug_role_cache()
    
    # 5. Принудительно обновляем кеш
    print("\n5. Принудительно обновляем кеш...")
    await force_update_role_cache()
    
    # 6. Проверяем финальное состояние
    print("\n6. Финальное состояние:")
    await test_role_cache_for_user(telegram_id)
    
    await redis_manager.disconnect()


async def main():
    """Главная функция"""
    if len(sys.argv) > 1:
        try:
            telegram_id = int(sys.argv[1])
            await test_user_role_changes(telegram_id)
        except ValueError:
            print("❌ Telegram ID должен быть числом")
    else:
        # Тестируем с пользователем из задачи
        await test_user_role_changes(7265679697)


if __name__ == "__main__":
    asyncio.run(main())
