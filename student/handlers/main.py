from aiogram import Router, F
from aiogram.types import Message, CallbackQuery
from aiogram.filters import CommandStart
from aiogram.fsm.context import FSMContext
from aiogram.fsm.state import StatesGroup, State
from ..keyboards.main import get_student_main_menu_kb

router = Router()

class StudentMainStates(StatesGroup):
    main = State()

# Обработчик /start убран - используется общий в main.py

async def show_student_main_menu(message: Message, user_role: str = None):
    """Возврат в главное меню студента"""
    # Студенческое меню доступно всем (включая админов и новых пользователей)
    # Не делаем проверку роли, так как это меню по умолчанию

    await message.answer(
        "Привет 👋\n"
        "Здесь ты можешь проходить домашки, прокачивать темы, отслеживать свой прогресс и готовиться к ЕНТ.\n"
        "Ниже — все разделы, которые тебе доступны:",
        reply_markup=get_student_main_menu_kb()
    )